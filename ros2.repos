repositories:
  ament/ament_cmake:
    type: git
    url: https://bgithub.xyz/ament/ament_cmake.git
    version: humble
  ament/ament_index:
    type: git
    url: https://bgithub.xyz/ament/ament_index.git
    version: humble
  ament/ament_lint:
    type: git
    url: https://bgithub.xyz/ament/ament_lint.git
    version: humble
  ament/ament_package:
    type: git
    url: https://bgithub.xyz/ament/ament_package.git
    version: humble
  ament/google_benchmark_vendor:
    type: git
    url: https://bgithub.xyz/ament/google_benchmark_vendor.git
    version: humble
  ament/googletest:
    type: git
    url: https://bgithub.xyz/ament/googletest.git
    version: humble
  ament/uncrustify_vendor:
    type: git
    url: https://bgithub.xyz/ament/uncrustify_vendor.git
    version: humble
  eProsima/Fast-CDR:
    type: git
    url: https://bgithub.xyz/eProsima/Fast-CDR.git
    version: v1.0.24
  eProsima/Fast-DDS:
    type: git
    url: https://bgithub.xyz/eProsima/Fast-DDS.git
    version: 2.6.x
  eProsima/foonathan_memory_vendor:
    type: git
    url: https://bgithub.xyz/eProsima/foonathan_memory_vendor.git
    version: master
  eclipse-cyclonedds/cyclonedds:
    type: git
    url: https://bgithub.xyz/eclipse-cyclonedds/cyclonedds.git
    version: releases/0.10.x
  eclipse-iceoryx/iceoryx:
    type: git
    url: https://bgithub.xyz/eclipse-iceoryx/iceoryx.git
    version: release_2.0
  gazebo-release/gz_cmake2_vendor:
    type: git
    url: https://bgithub.xyz/gazebo-release/gz_cmake2_vendor.git
    version: humble
  gazebo-release/gz_math6_vendor:
    type: git
    url: https://bgithub.xyz/gazebo-release/gz_math6_vendor.git
    version: humble
  osrf/osrf_pycommon:
    type: git
    url: https://bgithub.xyz/osrf/osrf_pycommon.git
    version: master
  osrf/osrf_testing_tools_cpp:
    type: git
    url: https://bgithub.xyz/osrf/osrf_testing_tools_cpp.git
    version: humble
  ros-perception/image_common:
    type: git
    url: https://bgithub.xyz/ros-perception/image_common.git
    version: humble
  ros-perception/laser_geometry:
    type: git
    url: https://bgithub.xyz/ros-perception/laser_geometry.git
    version: humble
  ros-planning/navigation_msgs:
    type: git
    url: https://bgithub.xyz/ros-planning/navigation_msgs.git
    version: humble
  ros-tooling/keyboard_handler:
    type: git
    url: https://bgithub.xyz/ros-tooling/keyboard_handler.git
    version: humble
  ros-tooling/libstatistics_collector:
    type: git
    url: https://bgithub.xyz/ros-tooling/libstatistics_collector.git
    version: humble
  ros-visualization/interactive_markers:
    type: git
    url: https://bgithub.xyz/ros-visualization/interactive_markers.git
    version: humble
  ros-visualization/python_qt_binding:
    type: git
    url: https://bgithub.xyz/ros-visualization/python_qt_binding.git
    version: humble
  ros-visualization/qt_gui_core:
    type: git
    url: https://bgithub.xyz/ros-visualization/qt_gui_core.git
    version: humble
  ros-visualization/rqt:
    type: git
    url: https://bgithub.xyz/ros-visualization/rqt.git
    version: humble
  ros-visualization/rqt_action:
    type: git
    url: https://bgithub.xyz/ros-visualization/rqt_action.git
    version: humble
  ros-visualization/rqt_bag:
    type: git
    url: https://bgithub.xyz/ros-visualization/rqt_bag.git
    version: humble
  ros-visualization/rqt_console:
    type: git
    url: https://bgithub.xyz/ros-visualization/rqt_console.git
    version: humble
  ros-visualization/rqt_graph:
    type: git
    url: https://bgithub.xyz/ros-visualization/rqt_graph.git
    version: humble
  ros-visualization/rqt_msg:
    type: git
    url: https://bgithub.xyz/ros-visualization/rqt_msg.git
    version: humble
  ros-visualization/rqt_plot:
    type: git
    url: https://bgithub.xyz/ros-visualization/rqt_plot.git
    version: humble
  ros-visualization/rqt_publisher:
    type: git
    url: https://bgithub.xyz/ros-visualization/rqt_publisher.git
    version: humble
  ros-visualization/rqt_py_console:
    type: git
    url: https://bgithub.xyz/ros-visualization/rqt_py_console.git
    version: humble
  ros-visualization/rqt_reconfigure:
    type: git
    url: https://bgithub.xyz/ros-visualization/rqt_reconfigure.git
    version: humble
  ros-visualization/rqt_service_caller:
    type: git
    url: https://bgithub.xyz/ros-visualization/rqt_service_caller.git
    version: humble
  ros-visualization/rqt_shell:
    type: git
    url: https://bgithub.xyz/ros-visualization/rqt_shell.git
    version: humble
  ros-visualization/rqt_srv:
    type: git
    url: https://bgithub.xyz/ros-visualization/rqt_srv.git
    version: humble
  ros-visualization/rqt_topic:
    type: git
    url: https://bgithub.xyz/ros-visualization/rqt_topic.git
    version: humble
  ros-visualization/tango_icons_vendor:
    type: git
    url: https://bgithub.xyz/ros-visualization/tango_icons_vendor.git
    version: humble
  ros/class_loader:
    type: git
    url: https://bgithub.xyz/ros/class_loader.git
    version: humble
  ros/kdl_parser:
    type: git
    url: https://bgithub.xyz/ros/kdl_parser.git
    version: humble
  ros/pluginlib:
    type: git
    url: https://bgithub.xyz/ros/pluginlib.git
    version: humble
  ros/resource_retriever:
    type: git
    url: https://bgithub.xyz/ros/resource_retriever.git
    version: humble
  ros/robot_state_publisher:
    type: git
    url: https://bgithub.xyz/ros/robot_state_publisher.git
    version: humble
  ros/ros_environment:
    type: git
    url: https://bgithub.xyz/ros/ros_environment.git
    version: humble
  ros/ros_tutorials:
    type: git
    url: https://bgithub.xyz/ros/ros_tutorials.git
    version: humble
  ros/urdfdom:
    type: git
    url: https://bgithub.xyz/ros/urdfdom.git
    version: humble
  ros/urdfdom_headers:
    type: git
    url: https://bgithub.xyz/ros/urdfdom_headers.git
    version: humble
  ros2/ament_cmake_ros:
    type: git
    url: https://bgithub.xyz/ros2/ament_cmake_ros.git
    version: humble
  ros2/common_interfaces:
    type: git
    url: https://bgithub.xyz/ros2/common_interfaces.git
    version: humble
  ros2/console_bridge_vendor:
    type: git
    url: https://bgithub.xyz/ros2/console_bridge_vendor.git
    version: humble
  ros2/demos:
    type: git
    url: https://bgithub.xyz/ros2/demos.git
    version: humble
  ros2/eigen3_cmake_module:
    type: git
    url: https://bgithub.xyz/ros2/eigen3_cmake_module.git
    version: humble
  ros2/example_interfaces:
    type: git
    url: https://bgithub.xyz/ros2/example_interfaces.git
    version: humble
  ros2/examples:
    type: git
    url: https://bgithub.xyz/ros2/examples.git
    version: humble
  ros2/geometry2:
    type: git
    url: https://bgithub.xyz/ros2/geometry2.git
    version: humble
  ros2/launch:
    type: git
    url: https://bgithub.xyz/ros2/launch.git
    version: humble
  ros2/launch_ros:
    type: git
    url: https://bgithub.xyz/ros2/launch_ros.git
    version: humble
  ros2/libyaml_vendor:
    type: git
    url: https://bgithub.xyz/ros2/libyaml_vendor.git
    version: humble
  ros2/message_filters:
    type: git
    url: https://bgithub.xyz/ros2/message_filters.git
    version: humble
  ros2/mimick_vendor:
    type: git
    url: https://bgithub.xyz/ros2/mimick_vendor.git
    version: humble
  ros2/orocos_kdl_vendor:
    type: git
    url: https://bgithub.xyz/ros2/orocos_kdl_vendor.git
    version: humble
  ros2/performance_test_fixture:
    type: git
    url: https://bgithub.xyz/ros2/performance_test_fixture.git
    version: humble
  ros2/pybind11_vendor:
    type: git
    url: https://bgithub.xyz/ros2/pybind11_vendor.git
    version: humble
  ros2/python_cmake_module:
    type: git
    url: https://bgithub.xyz/ros2/python_cmake_module.git
    version: humble
  ros2/rcl:
    type: git
    url: https://bgithub.xyz/ros2/rcl.git
    version: humble
  ros2/rcl_interfaces:
    type: git
    url: https://bgithub.xyz/ros2/rcl_interfaces.git
    version: humble
  ros2/rcl_logging:
    type: git
    url: https://bgithub.xyz/ros2/rcl_logging.git
    version: humble
  ros2/rclcpp:
    type: git
    url: https://bgithub.xyz/ros2/rclcpp.git
    version: humble
  ros2/rclpy:
    type: git
    url: https://bgithub.xyz/ros2/rclpy.git
    version: humble
  ros2/rcpputils:
    type: git
    url: https://bgithub.xyz/ros2/rcpputils.git
    version: humble
  ros2/rcutils:
    type: git
    url: https://bgithub.xyz/ros2/rcutils.git
    version: humble
  ros2/realtime_support:
    type: git
    url: https://bgithub.xyz/ros2/realtime_support.git
    version: humble
  ros2/rmw:
    type: git
    url: https://bgithub.xyz/ros2/rmw.git
    version: humble
  ros2/rmw_connextdds:
    type: git
    url: https://bgithub.xyz/ros2/rmw_connextdds.git
    version: humble
  ros2/rmw_cyclonedds:
    type: git
    url: https://bgithub.xyz/ros2/rmw_cyclonedds.git
    version: humble
  ros2/rmw_dds_common:
    type: git
    url: https://bgithub.xyz/ros2/rmw_dds_common.git
    version: humble
  ros2/rmw_fastrtps:
    type: git
    url: https://bgithub.xyz/ros2/rmw_fastrtps.git
    version: humble
  ros2/rmw_implementation:
    type: git
    url: https://bgithub.xyz/ros2/rmw_implementation.git
    version: humble
  ros2/ros2_tracing:
    type: git
    url: https://bgithub.xyz/ros2/ros2_tracing.git
    version: humble
  ros2/ros2cli:
    type: git
    url: https://bgithub.xyz/ros2/ros2cli.git
    version: humble
  ros2/ros2cli_common_extensions:
    type: git
    url: https://bgithub.xyz/ros2/ros2cli_common_extensions.git
    version: humble
  ros2/ros_testing:
    type: git
    url: https://bgithub.xyz/ros2/ros_testing.git
    version: humble
  ros2/rosbag2:
    type: git
    url: https://bgithub.xyz/ros2/rosbag2.git
    version: humble
  ros2/rosidl:
    type: git
    url: https://bgithub.xyz/ros2/rosidl.git
    version: humble
  ros2/rosidl_dds:
    type: git
    url: https://bgithub.xyz/ros2/rosidl_dds.git
    version: humble
  ros2/rosidl_defaults:
    type: git
    url: https://bgithub.xyz/ros2/rosidl_defaults.git
    version: humble
  ros2/rosidl_python:
    type: git
    url: https://bgithub.xyz/ros2/rosidl_python.git
    version: humble
  ros2/rosidl_runtime_py:
    type: git
    url: https://bgithub.xyz/ros2/rosidl_runtime_py.git
    version: humble
  ros2/rosidl_typesupport:
    type: git
    url: https://bgithub.xyz/ros2/rosidl_typesupport.git
    version: humble
  ros2/rosidl_typesupport_fastrtps:
    type: git
    url: https://bgithub.xyz/ros2/rosidl_typesupport_fastrtps.git
    version: humble
  ros2/rpyutils:
    type: git
    url: https://bgithub.xyz/ros2/rpyutils.git
    version: humble
  ros2/rviz:
    type: git
    url: https://bgithub.xyz/ros2/rviz.git
    version: humble
  ros2/spdlog_vendor:
    type: git
    url: https://bgithub.xyz/ros2/spdlog_vendor.git
    version: humble
  ros2/sros2:
    type: git
    url: https://bgithub.xyz/ros2/sros2.git
    version: humble
  ros2/system_tests:
    type: git
    url: https://bgithub.xyz/ros2/system_tests.git
    version: humble
  ros2/test_interface_files:
    type: git
    url: https://bgithub.xyz/ros2/test_interface_files.git
    version: humble
  ros2/tinyxml2_vendor:
    type: git
    url: https://bgithub.xyz/ros2/tinyxml2_vendor.git
    version: humble
  ros2/tinyxml_vendor:
    type: git
    url: https://bgithub.xyz/ros2/tinyxml_vendor.git
    version: humble
  ros2/tlsf:
    type: git
    url: https://bgithub.xyz/ros2/tlsf.git
    version: humble
  ros2/unique_identifier_msgs:
    type: git
    url: https://bgithub.xyz/ros2/unique_identifier_msgs.git
    version: humble
  ros2/urdf:
    type: git
    url: https://bgithub.xyz/ros2/urdf.git
    version: humble
  ros2/yaml_cpp_vendor:
    type: git
    url: https://bgithub.xyz/ros2/yaml_cpp_vendor.git
    version: humble
