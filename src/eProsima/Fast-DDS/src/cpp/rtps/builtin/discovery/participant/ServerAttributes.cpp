// Copyright 2022 Proyectos y Sistemas de Mantenimiento SL (eProsima).
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/**
 * @file ServerAttributes.cpp
 *
 */

#include <fastdds/rtps/attributes/ServerAttributes.h>

namespace eprosima {
namespace fastdds {
namespace rtps {

template<> const char* server_ostream_separators<char>::list_separator = "; ";
template<> const wchar_t* server_ostream_separators<wchar_t>::list_separator = L"; ";

template<> const char* server_ostream_separators<char>::locator_separator = "|";
template<> const wchar_t* server_ostream_separators<wchar_t>::locator_separator = L"|";

} /* namespace rtps */
} /* namespace fastdds */
} /* namespace eprosima */
