<?xml version="1.0" encoding="UTF-8" ?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="http://www.eprosima.com/XMLSchemas/fastRTPS_Profiles"
           targetNamespace="http://www.eprosima.com/XMLSchemas/fastRTPS_Profiles"
           attributeFormDefault="unqualified"
           elementFormDefault="qualified">

    <xs:simpleType name="stringType">
        <xs:restriction base="xs:string"/>
    </xs:simpleType>

      <xs:simpleType name="idType">
        <xs:restriction base="xs:unsignedShort"/>
    </xs:simpleType>

    <xs:simpleType name="int8Type">
        <xs:restriction base="xs:byte"/>
    </xs:simpleType>

    <xs:simpleType name="uint8Type">
        <xs:restriction base="xs:unsignedByte"/>
    </xs:simpleType>

    <xs:simpleType name="int32Type">
        <xs:restriction base="xs:int"/>
    </xs:simpleType>

    <xs:simpleType name="uint32Type">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>

    <xs:simpleType name="int16Type">
        <xs:restriction base="xs:short"/>
    </xs:simpleType>

    <xs:simpleType name="uint16Type">
        <xs:restriction base="xs:unsignedShort"/>
    </xs:simpleType>

    <xs:simpleType name="boolType">
        <xs:restriction base="xs:boolean"/> <!-- {true, false} -->
    </xs:simpleType>

    <xs:simpleType name="historyMemoryPolicyType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PREALLOCATED"/>
            <xs:enumeration value="PREALLOCATED_WITH_REALLOC"/>
            <xs:enumeration value="DYNAMIC"/>
            <xs:enumeration value="DYNAMIC_REUSABLE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="tlsOptionsType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DEFAULT_WORKAROUNDS"/>
            <xs:enumeration value="NO_COMPRESSION"/>
            <xs:enumeration value="NO_SSLV2"/>
            <xs:enumeration value="NO_SSLV3"/>
            <xs:enumeration value="NO_TLSV1"/>
            <xs:enumeration value="NO_TLSV1_1"/>
            <xs:enumeration value="NO_TLSV1_2"/>
            <xs:enumeration value="NO_TLSV1_3"/>
            <xs:enumeration value="SINGLE_DH_USE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="tlsOptionsVectorType">
        <xs:sequence>
            <xs:element name="option" type="tlsOptionsType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="tlsVerifyPathVectorType">
        <xs:sequence>
            <xs:element name="verify_path" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="tlsVerifyModeType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="VERIFY_NONE"/>
            <xs:enumeration value="VERIFY_PEER"/>
            <xs:enumeration value="VERIFY_FAIL_IF_NO_PEER_CERT"/>
            <xs:enumeration value="VERIFY_CLIENT_ONCE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="tlsVerifyModeVectorType">
        <xs:sequence>
            <xs:element name="verify" type="tlsVerifyModeType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="tlsHandShakeRole">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DEFAULT"/>
            <xs:enumeration value="CLIENT"/>
            <xs:enumeration value="SERVER"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="tlsConfigType">
        <xs:all minOccurs="0">
            <xs:element name="password" type="stringType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="options" type="tlsOptionsVectorType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="cert_chain_file" type="stringType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="private_key_file" type="stringType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="tmp_dh_file" type="stringType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="verify_file" type="stringType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="verify_mode" type="tlsVerifyModeVectorType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="verify_paths" type="tlsVerifyPathVectorType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="default_verify_path" type="xs:boolean" minOccurs="0" maxOccurs="1"/>
            <xs:element name="verify_depth" type="xs:int" minOccurs="0" maxOccurs="1"/>
            <xs:element name="rsa_private_key_file" type="stringType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="handshake_role" type="tlsHandShakeRole" minOccurs="0" maxOccurs="1"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="octetVectorType">
        <xs:all>
            <xs:element name="value" type="xs:string"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="propertyType">
        <xs:all>
            <xs:element name="name" type="stringType" minOccurs="1" />
            <xs:element name="value" type="stringType" minOccurs="1"/>
            <xs:element name="propagate" type="boolType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="binaryPropertyType">
        <xs:all>
            <xs:element name="name" type="stringType" minOccurs="1"/>
            <xs:element name="value" type="stringType" minOccurs="0"/><!-- std::vector<uint8_t> -->
            <xs:element name="propagate" type="boolType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="propertyVectorType">
        <xs:sequence>
            <xs:element name="property" type="propertyType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="binaryPropertyVectorType">
        <xs:sequence>
            <xs:element name="property" type="binaryPropertyType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="nonNegativeInteger_Duration_SEC">
        <xs:union>
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:pattern value="\s*(DURATION_INFINITY|DURATION_INFINITE_SEC)\s*"/>
                </xs:restriction>
            </xs:simpleType>
            <xs:simpleType>
                <xs:restriction base="xs:unsignedInt"/>
            </xs:simpleType>
        </xs:union>
    </xs:simpleType>

    <xs:simpleType name="nonNegativeInteger_Duration_NSEC">
        <xs:union>
            <xs:simpleType>
                <xs:restriction base="xs:string">
                    <xs:pattern value="\s*(DURATION_INFINITY|DURATION_INFINITE_NSEC)\s*"/>
                </xs:restriction>
            </xs:simpleType>
            <xs:simpleType>
                <xs:restriction base="xs:unsignedInt"/>
            </xs:simpleType>
        </xs:union>
    </xs:simpleType>

    <!-- xml schema constrains doesn't allow to validate that:
        + durationType has either text or tags
        + durationType associated text matches DURATION_INFINITY
     -->

    <xs:complexType name="durationType" mixed="true">
        <xs:sequence>
            <xs:choice minOccurs="0">
                <xs:sequence>
                    <xs:element name="sec" type="nonNegativeInteger_Duration_SEC" default="0" minOccurs="1" maxOccurs="1"/>
                    <xs:element name="nanosec" type="nonNegativeInteger_Duration_NSEC" default="0" minOccurs="0" maxOccurs="1"/>
                </xs:sequence>
                <xs:sequence>
                    <xs:element name="nanosec" type="nonNegativeInteger_Duration_NSEC" default="0" minOccurs="1" maxOccurs="1"/>
                    <xs:element name="sec" type="nonNegativeInteger_Duration_SEC" default="0" minOccurs="0" maxOccurs="1"/>
                </xs:sequence>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="writerTimesType">
        <xs:all minOccurs="0">
            <xs:element name="initialHeartbeatDelay" type="durationType" minOccurs="0"/>
            <xs:element name="heartbeatPeriod" type="durationType" minOccurs="0"/>
            <xs:element name="nackResponseDelay" type="durationType" minOccurs="0"/>
            <xs:element name="nackSupressionDuration" type="durationType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="readerTimesType">
        <xs:all minOccurs="0">
            <xs:element name="initialAcknackDelay" type="durationType" minOccurs="0"/>
            <xs:element name="heartbeatResponseDelay" type="durationType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="portType">
        <xs:all minOccurs="0">
            <xs:element name="portBase" type="uint16Type" minOccurs="0"/>
            <xs:element name="domainIDGain" type="uint16Type" minOccurs="0"/>
            <xs:element name="participantIDGain" type="uint16Type" minOccurs="0"/>
            <xs:element name="offsetd0" type="uint16Type" minOccurs="0"/>
            <xs:element name="offsetd1" type="uint16Type" minOccurs="0"/>
            <xs:element name="offsetd2" type="uint16Type" minOccurs="0"/>
            <xs:element name="offsetd3" type="uint16Type" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:simpleType name="ipv4AddressType">
        <xs:restriction base="xs:string">
            <xs:pattern value="((1?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\.){3}(1?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])" />
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ipv6AddressType">
        <xs:restriction base="xs:string">
            <xs:pattern value="::[A-Fa-f0-9]{4}|([A-Fa-f0-9]{1,4}:){7}[A-Fa-f0-9]{1,4}" />
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="udpv4LocatorType">
        <xs:all minOccurs="0">
            <xs:element name="port" type="uint32Type" minOccurs="0"/>
            <xs:element name="address" type="stringType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="udpv6LocatorType">
        <xs:all minOccurs="0">
            <xs:element name="port" type="uint32Type" minOccurs="0"/>
            <xs:element name="address" type="stringType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="tcpv4LocatorType">
        <xs:all minOccurs="0">
            <xs:element name="port" type="uint16Type" minOccurs="0"/>
            <xs:element name="physical_port" type="uint16Type" minOccurs="0"/>
            <xs:element name="address" type="stringType" minOccurs="0"/>
            <xs:element name="wan_address" type="stringType" minOccurs="0"/>
            <xs:element name="unique_lan_id" type="stringType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="tcpv6LocatorType">
        <xs:choice>
            <xs:element name="port" type="uint16Type" minOccurs="0"/>
            <xs:element name="physical_port" type="uint16Type" minOccurs="0"/>
            <xs:element name="address" type="stringType" minOccurs="0"/>
        </xs:choice>
    </xs:complexType>

    <xs:complexType name="locatorType">
        <xs:choice>
            <xs:element name="udpv4" type="udpv4LocatorType"/>
            <xs:element name="udpv6" type="udpv6LocatorType"/>
            <xs:element name="tcpv4" type="tcpv4LocatorType"/>
            <xs:element name="tcpv6" type="tcpv6LocatorType"/>
        </xs:choice>
    </xs:complexType>

    <xs:complexType name="locatorListType">
        <xs:sequence>
            <xs:element name="locator" type="locatorType" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="simpleEDPType">
        <xs:all minOccurs="0">
            <xs:element name="PUBWRITER_SUBREADER" type="boolType"/>
            <xs:element name="PUBREADER_SUBWRITER" type="boolType"/>
        </xs:all>
    </xs:complexType>

    <xs:simpleType name="DiscoveryProtocol">
        <xs:restriction base="xs:string">
            <xs:enumeration value="NONE"/>
            <xs:enumeration value="SIMPLE"/>
            <xs:enumeration value="CLIENT"/>
            <xs:enumeration value="SERVER"/>
            <xs:enumeration value="BACKUP"/>
            <xs:enumeration value="SUPER_CLIENT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="ParticipantFlags">
        <xs:restriction base="xs:string">
            <xs:pattern value="((FILTER_DIFFERENT_HOST|FILTER_DIFFERENT_PROCESS|FILTER_SAME_PROCESS|NO_FILTER)(\||\s)*)*" />
        </xs:restriction>
    </xs:simpleType>

   <xs:simpleType name="EDPType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="SIMPLE"/>
            <xs:enumeration value="STATIC"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="guid">
        <xs:restriction base="xs:string">
          <xs:pattern value="([0-9a-fA-F]{1,2}\.){11}[0-9a-fA-F]{1,2}" />
      <xs:whiteSpace value='collapse'/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="RemoteServerAttributes">
        <xs:choice>
            <xs:sequence>
                <xs:element name="metatrafficUnicastLocatorList" type="locatorListType" minOccurs="1" />
                <xs:element name="metatrafficMulticastLocatorList" type="locatorListType" minOccurs="0" />
            </xs:sequence>
            <xs:sequence>
                <xs:element name="metatrafficMulticastLocatorList" type="locatorListType" minOccurs="1" />
                <xs:element name="metatrafficUnicastLocatorList" type="locatorListType" minOccurs="0" />
            </xs:sequence>
        </xs:choice>
        <xs:attribute name="prefix" type="guid" use="required"/>
    </xs:complexType>

    <xs:complexType name="DiscoveryServerList">
        <xs:sequence>
            <xs:element name="RemoteServer" type="RemoteServerAttributes" minOccurs="1" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="initialAnnouncementsType">
        <xs:all minOccurs="0">
            <xs:element name="count" type="uint32Type" minOccurs="0"/>
            <xs:element name="period" type="durationType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="discoverySettingsType">
        <xs:all minOccurs="0">
            <xs:element name="discoveryProtocol" type="DiscoveryProtocol" minOccurs="0"/>
            <xs:element name="ignoreParticipantFlags" type="ParticipantFlags" minOccurs="0"/>
            <xs:element name="EDP" type="EDPType" minOccurs="0"/>
            <xs:element name="leaseDuration" type="durationType" minOccurs="0"/>
            <xs:element name="leaseAnnouncement" type="durationType" minOccurs="0"/>
            <xs:element name="initialAnnouncements" type="initialAnnouncementsType" minOccurs="0"/>
            <xs:element name="simpleEDP" type="simpleEDPType" minOccurs="0"/>
            <xs:element name="clientAnnouncementPeriod" type="durationType" minOccurs="0"/>
            <xs:element name="discoveryServersList" type="DiscoveryServerList" minOccurs="0"/>
            <xs:element name="staticEndpointXMLFilename" type="stringType" minOccurs="0"/>
            <xs:element name="static_edp_xml_config" type="stringType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="builtinAttributesType">
        <xs:all minOccurs="0">
            <xs:element name="discovery_config" type="discoverySettingsType" minOccurs="0"/>
            <xs:element name="use_WriterLivelinessProtocol" type="boolType" minOccurs="0"/>
            <xs:element name="metatrafficUnicastLocatorList" type="locatorListType" minOccurs="0"/>
            <xs:element name="metatrafficMulticastLocatorList" type="locatorListType" minOccurs="0"/>
            <xs:element name="initialPeersList" type="locatorListType" minOccurs="0"/>
            <xs:element name="readerHistoryMemoryPolicy" type="historyMemoryPolicyType" minOccurs="0"/>
            <xs:element name="writerHistoryMemoryPolicy" type="historyMemoryPolicyType" minOccurs="0"/>
            <xs:element name="readerPayloadSize" type="uint32Type" minOccurs="0"/>
            <xs:element name="writerPayloadSize" type="uint32Type" minOccurs="0"/>
            <xs:element name="mutation_tries" type="uint32Type" minOccurs="0"/>
            <xs:element name="avoid_builtin_multicast" type="boolType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="throughputControllerType">
        <xs:all minOccurs="0">
            <xs:element name="bytesPerPeriod" type="uint32Type" minOccurs="0"/>
            <xs:element name="periodMillisecs" type="uint32Type" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="resourceLimitsQosPolicyType">
        <xs:all minOccurs="0">
            <xs:element name="max_samples" type="int32Type" minOccurs="0"/>
            <xs:element name="max_instances" type="int32Type" minOccurs="0"/>
            <xs:element name="max_samples_per_instance" type="int32Type" minOccurs="0"/>
            <xs:element name="allocated_samples" type="int32Type" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="containerAllocationConfigType">
        <xs:all minOccurs="0">
            <xs:element name="initial" type="uint32Type" minOccurs="0"/>
            <xs:element name="maximum" type="uint32Type" minOccurs="0"/>
            <xs:element name="increment" type="uint32Type" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="remoteLocatorsAllocationConfigType">
        <xs:all minOccurs="0">
            <xs:element name="max_unicast_locators" type="uint32Type" minOccurs="0"/>
            <xs:element name="max_multicast_locators" type="uint32Type" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="sendBuffersAllocationConfigType">
        <xs:all minOccurs="0">
            <xs:element name="preallocated_number" type="uint32Type" minOccurs="0"/>
            <xs:element name="dynamic" type="boolType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="rtpsParticipantAllocationAttributesType">
        <xs:all minOccurs="0">
            <xs:element name="remote_locators" type="remoteLocatorsAllocationConfigType" minOccurs="0"/>
            <xs:element name="total_participants" type="containerAllocationConfigType" minOccurs="0"/>
            <xs:element name="total_readers" type="containerAllocationConfigType" minOccurs="0"/>
            <xs:element name="total_writers" type="containerAllocationConfigType" minOccurs="0"/>
            <xs:element name="send_buffers" type="sendBuffersAllocationConfigType" minOccurs="0"/>
            <xs:element name="max_properties" type="uint32Type" minOccurs="0"/>
            <xs:element name="max_user_data" type="uint32Type" minOccurs="0"/>
            <xs:element name="max_partitions" type="uint32Type" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:simpleType name="historyQosKindType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="KEEP_LAST"/>
            <xs:enumeration value="KEEP_ALL"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="historyQosPolicyType">
        <xs:all minOccurs="0">
            <xs:element name="kind" type="historyQosKindType" minOccurs="0"/>
            <xs:element name="depth" type="int32Type" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:simpleType name="topicKindType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="NO_KEY"/>
            <xs:enumeration value="WITH_KEY"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="topicAttributesType">
        <xs:all minOccurs="0">
            <xs:element name="kind" type="topicKindType" minOccurs="0"/>
            <xs:element name="name" type="stringType" minOccurs="0"/>
            <xs:element name="dataType" type="stringType" minOccurs="0"/>
            <xs:element name="historyQos" type="historyQosPolicyType" minOccurs="0"/>
            <xs:element name="resourceLimitsQos" type="resourceLimitsQosPolicyType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

   <xs:simpleType name="durabilityQosKindType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="VOLATILE"/>
            <xs:enumeration value="TRANSIENT_LOCAL"/>
            <xs:enumeration value="TRANSIENT"/>
            <xs:enumeration value="PERSISTENT"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="durabilityQosPolicyType">
        <xs:all>
            <xs:element name="kind" type="durabilityQosKindType"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="durabilityServiceQosPolicyType">
        <xs:all minOccurs="0">
            <xs:element name="service_cleanup_delay" type="durationType" minOccurs="0"/>
            <xs:element name="history_kind" type="historyQosKindType" minOccurs="0"/>
            <xs:element name="history_depth" type="uint32Type" minOccurs="0"/>
            <xs:element name="max_samples" type="uint32Type" minOccurs="0"/>
            <xs:element name="max_instances" type="uint32Type" minOccurs="0"/>
            <xs:element name="max_samples_per_instance" type="uint32Type" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="deadlineQosPolicyType">
        <xs:all>
            <xs:element name="period" type="durationType"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="latencyBudgetQosPolicyType">
        <xs:all>
            <xs:element name="duration" type="durationType"/>
        </xs:all>
    </xs:complexType>

    <xs:simpleType name="livelinessQosKindType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="AUTOMATIC"/>
            <xs:enumeration value="MANUAL_BY_PARTICIPANT"/>
            <xs:enumeration value="MANUAL_BY_TOPIC"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="livelinessQosPolicyType">
        <xs:all minOccurs="0">
            <xs:element name="kind" type="livelinessQosKindType" minOccurs="0"/>
            <xs:element name="lease_duration" type="durationType" minOccurs="0"/>
            <xs:element name="announcement_period" type="durationType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:simpleType name="reliabilityQosKindType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="BEST_EFFORT"/>
            <xs:enumeration value="RELIABLE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="reliabilityQosPolicyType">
        <xs:all>
            <xs:element name="kind" type="reliabilityQosKindType" minOccurs="0"/>
            <xs:element name="max_blocking_time" type="durationType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="lifespanQosPolicyType">
        <xs:all>
            <xs:element name="duration" type="durationType"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="timeBasedFilterQosPolicyType">
        <xs:all>
            <xs:element name="minimum_separation" type="durationType"/>
        </xs:all>
    </xs:complexType>

    <xs:simpleType name="ownershipQosKindType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="SHARED"/>
            <xs:enumeration value="EXCLUSIVE"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="ownershipQosPolicyType">
        <xs:all>
            <xs:element name="kind" type="ownershipQosKindType"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="ownershipStrengthQosPolicyType">
        <xs:all>
            <xs:element name="value" type="uint32Type"/>
        </xs:all>
    </xs:complexType>

    <xs:simpleType name="destinationOrderQosKindType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="BY_RECEPTION_TIMESTAMP"/>
            <xs:enumeration value="BY_SOURCE_TIMESTAMP"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="destinationOrderQosPolicyType">
        <xs:all>
            <xs:element name="kind" type="destinationOrderQosKindType"/>
        </xs:all>
    </xs:complexType>

    <xs:simpleType name="presentationQosKindType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INSTANCE"/>
            <xs:enumeration value="TOPIC"/>
            <xs:enumeration value="GROUP"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="presentationQosPolicyType">
        <xs:all minOccurs="0">
            <xs:element name="access_scope" type="presentationQosKindType" minOccurs="0"/>
            <xs:element name="coherent_access" type="boolType" minOccurs="0"/>
            <xs:element name="ordered_access" type="boolType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="nameVectorType">
        <xs:sequence>
            <xs:element name="name" type="stringType" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="partitionQosPolicyType">
        <xs:all>
            <xs:element name="names" type="nameVectorType"/>
        </xs:all>
    </xs:complexType>

    <xs:simpleType name="publishModeQosKindType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="SYNCHRONOUS"/>
            <xs:enumeration value="ASYNCHRONOUS"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="publishModeQosPolicyType">
        <xs:all>
            <xs:element name="kind" type="publishModeQosKindType"/>
            <xs:element name="flow_controller_name" type="string" minOccurs="0" maxOccurs="1"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="propertyPolicyType">
        <xs:all minOccurs="0">
            <xs:element name="properties" type="propertyVectorType" minOccurs="0"/>
            <xs:element name="binary_properties" type="binaryPropertyVectorType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <!--Flow Controller Descriptor List Type:
        └ flow_controller_descriptor [1~*]-->
    <xs:complexType name="flowControllerDescriptorListType">
        <xs:sequence minOccurs="1" maxOccurs="unbounded">   <!-- multiple instances of the elements -->
            <xs:element name="flow_controller_descriptor" type="flowControllerDescriptorType"/>
        </xs:sequence>
    </xs:complexType>

    <!--Flow Controller Descriptor Type:
        ├ name                  [string] req,
        ├ scheduler             [flowControllerSchedulerPolicy],
        ├ max_bytes_per_period  [int32],
        ├ period_ms             [uint64]-->
    <xs:complexType name="flowControllerDescriptorType">
        <xs:all>
            <xs:element name="name" type="string" minOccurs="1" maxOccurs="1"/>
            <xs:element name="scheduler" type="flowControllerSchedulerPolicy" minOccurs="0" maxOccurs="1"/>
            <xs:element name="max_bytes_per_period" type="int32" minOccurs="0" maxOccurs="1"/>
            <xs:element name="period_ms" type="uint64" minOccurs="0" maxOccurs="1"/>
        </xs:all>
    </xs:complexType>

    <!--Flow Controller Scheduler Policy Type [string]:
         ("FIFO", "ROUND_ROBIN", "HIGH_PRIORITY", "PRIORITY_WITH_RESERVATION")-->
    <xs:simpleType name="flowControllerSchedulerPolicy">
        <xs:restriction base="xs:string">
            <xs:enumeration value="FIFO" />
            <xs:enumeration value="ROUND_ROBIN" />
            <xs:enumeration value="HIGH_PRIORITY" />
            <xs:enumeration value="PRIORITY_WITH_RESERVATION" />
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="domainIdVectorType">
        <xs:sequence>
            <xs:element name="domainId" type="uint16Type" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="datasharingQosKindType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ON"/>
            <xs:enumeration value="OFF"/>
            <xs:enumeration value="AUTOMATIC"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="dataSharingQosPolicyType">
        <xs:all>
            <xs:element name="kind" type="datasharingQosKindType" minOccurs="1"/>
            <xs:element name="shared_dir" type="stringType" minOccurs="0"/>
            <xs:element name="domain_ids" type="domainIdVectorType" minOccurs="0"/>
            <xs:element name="max_domains" type="uint32Type" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="disablePositiveAcksQosPolicyType">
        <xs:all>
            <xs:element name="enabled" type="boolType"/>
            <xs:element name="duration" type="durationType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="writerQosPoliciesType">
        <xs:all minOccurs="0">
            <xs:element name="durability" type="durabilityQosPolicyType" minOccurs="0"/>
            <xs:element name="durabilityService" type="durabilityServiceQosPolicyType" minOccurs="0"/>
            <xs:element name="deadline" type="deadlineQosPolicyType" minOccurs="0"/>
            <xs:element name="latencyBudget" type="latencyBudgetQosPolicyType" minOccurs="0"/>
            <xs:element name="liveliness" type="livelinessQosPolicyType" minOccurs="0"/>
            <xs:element name="reliability" type="reliabilityQosPolicyType" minOccurs="0"/>
            <xs:element name="lifespan" type="lifespanQosPolicyType" minOccurs="0"/>
            <xs:element name="userData" type="octetVectorType" minOccurs="0"/>
            <xs:element name="timeBasedFilter" type="timeBasedFilterQosPolicyType" minOccurs="0"/>
            <xs:element name="ownership" type="ownershipQosPolicyType" minOccurs="0"/>
            <xs:element name="ownershipStrength" type="ownershipStrengthQosPolicyType" minOccurs="0"/>
            <xs:element name="destinationOrder" type="destinationOrderQosPolicyType" minOccurs="0"/>
            <xs:element name="presentation" type="presentationQosPolicyType" minOccurs="0"/>
            <xs:element name="partition" type="partitionQosPolicyType" minOccurs="0"/>
            <xs:element name="topicData" type="octetVectorType" minOccurs="0"/>
            <xs:element name="groupData" type="octetVectorType" minOccurs="0"/>
            <xs:element name="publishMode" type="publishModeQosPolicyType" minOccurs="0"/>
            <xs:element name="data_sharing" type="dataSharingQosPolicyType" minOccurs="0"/>
            <xs:element name="disablePositiveAcks" type="disablePositiveAcksQosPolicyType" minOccurs="0"/>
            <xs:element name="disable_heartbeat_piggyback" type="boolType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="readerQosPoliciesType">
        <xs:all minOccurs="0">
            <xs:element name="durability" type="durabilityQosPolicyType" minOccurs="0"/>
            <xs:element name="durabilityService" type="durabilityServiceQosPolicyType" minOccurs="0"/>
            <xs:element name="deadline" type="deadlineQosPolicyType" minOccurs="0"/>
            <xs:element name="latencyBudget" type="latencyBudgetQosPolicyType" minOccurs="0"/>
            <xs:element name="liveliness" type="livelinessQosPolicyType" minOccurs="0"/>
            <xs:element name="reliability" type="reliabilityQosPolicyType" minOccurs="0"/>
            <xs:element name="lifespan" type="lifespanQosPolicyType" minOccurs="0"/>
            <xs:element name="userData" type="octetVectorType" minOccurs="0"/>
            <xs:element name="timeBasedFilter" type="timeBasedFilterQosPolicyType" minOccurs="0"/>
            <xs:element name="ownership" type="ownershipQosPolicyType" minOccurs="0"/>
            <xs:element name="destinationOrder" type="destinationOrderQosPolicyType" minOccurs="0"/>
            <xs:element name="presentation" type="presentationQosPolicyType" minOccurs="0"/>
            <xs:element name="partition" type="partitionQosPolicyType" minOccurs="0"/>
            <xs:element name="topicData" type="octetVectorType" minOccurs="0"/>
            <xs:element name="groupData" type="octetVectorType" minOccurs="0"/>
            <xs:element name="data_sharing" type="dataSharingQosPolicyType" minOccurs="0"/>
            <xs:element name="disablePositiveAcks" type="disablePositiveAcksQosPolicyType" minOccurs="0"/>
        </xs:all>
    </xs:complexType>

    <!-- |||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||| -->

    <xs:complexType name="rtpsParticipantAttributesType">
        <xs:all minOccurs="0">
            <xs:element name="allocation" type="rtpsParticipantAllocationAttributesType" minOccurs="0"/>
            <xs:element name="prefix" type="guid" minOccurs="0"/>
            <xs:element name="defaultUnicastLocatorList" type="locatorListType" minOccurs="0"/>
            <xs:element name="defaultMulticastLocatorList" type="locatorListType" minOccurs="0"/>
            <xs:element name="sendSocketBufferSize" type="uint32Type" minOccurs="0"/>
            <xs:element name="listenSocketBufferSize" type="uint32Type" minOccurs="0"/>
            <xs:element name="builtin" type="builtinAttributesType" minOccurs="0"/>
            <xs:element name="port" type="portType" minOccurs="0"/>
            <xs:element name="userData" type="octetVectorType" minOccurs="0"/>
            <xs:element name="participantID" type="int32Type" minOccurs="0"/>
            <xs:element name="throughputController" type="throughputControllerType" minOccurs="0"/>
            <xs:element name="userTransports" type="stringListType" minOccurs="0"/>
            <xs:element name="useBuiltinTransports" type="boolType" minOccurs="0"/>
            <xs:element name="builtinTransports" minOccurs="0" maxOccurs="1">
                            <xs:simpleType>
                                <xs:restriction base="xs:string">
                                    <xs:enumeration value="NONE"/>
                                    <xs:enumeration value="DEFAULT"/>
                                    <xs:enumeration value="DEFAULTv6"/>
                                    <xs:enumeration value="SHM"/>
                                    <xs:enumeration value="UDPv4"/>
                                    <xs:enumeration value="UDPv6"/>
                                    <xs:enumeration value="LARGE_DATA"/>
                                    <xs:enumeration value="LARGE_DATAv6"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
            <xs:element name="propertiesPolicy" type="propertyPolicyType" minOccurs="0"/>
            <xs:element name="name" type="stringType" minOccurs="0"/>
            <xs:element name="flow_controller_descriptor_list" type="flowControllerDescriptorListType" minOccurs="0" maxOccurs="1"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="participantProfileType">
        <xs:sequence>
            <xs:element name="domainId" type="uint32Type" minOccurs="0"/>
            <xs:element name="rtps" type="rtpsParticipantAttributesType"/>
        </xs:sequence>
        <xs:attribute name="profile_name" type="stringType" use="required"/>
        <xs:attribute name="is_default_profile" type="boolean" use="optional"/>
    </xs:complexType>

    <xs:complexType name="publisherProfileType">
        <xs:all minOccurs="0">
            <xs:element name="topic" type="topicAttributesType" minOccurs="0"/>
            <xs:element name="qos" type="writerQosPoliciesType" minOccurs="0"/>
            <xs:element name="times" type="writerTimesType" minOccurs="0"/>
            <xs:element name="unicastLocatorList" type="locatorListType" minOccurs="0"/>
            <xs:element name="multicastLocatorList" type="locatorListType" minOccurs="0"/>
            <xs:element name="throughputController" type="throughputControllerType" minOccurs="0"/>
            <xs:element name="historyMemoryPolicy" type="historyMemoryPolicyType" minOccurs="0"/>
            <xs:element name="propertiesPolicy" type="propertyPolicyType" minOccurs="0"/>
            <xs:element name="userDefinedID" type="int16Type" minOccurs="0"/>
            <xs:element name="entityID" type="int16Type" minOccurs="0"/>
            <xs:element name="matchedSubscribersAllocation" type="containerAllocationConfigType" minOccurs="0"/>
        </xs:all>
        <xs:attribute name="profile_name" type="stringType" use="required"/>
        <xs:attribute name="is_default_profile" type="boolean" use="optional"/>
    </xs:complexType>

    <xs:complexType name="subscriberProfileType">
        <xs:all minOccurs="0">
            <xs:element name="topic" type="topicAttributesType" minOccurs="0"/>
            <xs:element name="qos" type="readerQosPoliciesType" minOccurs="0"/>
            <xs:element name="times" type="readerTimesType" minOccurs="0"/>
            <xs:element name="unicastLocatorList" type="locatorListType" minOccurs="0"/>
            <xs:element name="multicastLocatorList" type="locatorListType" minOccurs="0"/>
            <xs:element name="expectsInlineQos" type="boolType" minOccurs="0"/>
            <xs:element name="historyMemoryPolicy" type="historyMemoryPolicyType" minOccurs="0"/>
            <xs:element name="propertiesPolicy" type="propertyPolicyType" minOccurs="0"/>
            <xs:element name="userDefinedID" type="int16Type" minOccurs="0"/>
            <xs:element name="entityID" type="int16Type" minOccurs="0"/>
            <xs:element name="matchedPublishersAllocation" type="containerAllocationConfigType" minOccurs="0"/>
        </xs:all>
        <xs:attribute name="profile_name" type="stringType" use="required"/>
        <xs:attribute name="is_default_profile" type="boolean" use="optional"/>
    </xs:complexType>

    <xs:complexType name="requesterProfileType">
        <xs:all>
            <xs:element name="request_topic_name" type="stringType" minOccurs="0"/>
            <xs:element name="reply_topic_name" type="stringType" minOccurs="0"/>
            <xs:element name="publisher" type="publisherProfileType"/>
            <xs:element name="subscriber" type="subscriberProfileType"/>
        </xs:all>
        <xs:attribute name="profile_name" type="stringType" use="required"/>
        <xs:attribute name="service_name" type="stringType" use="required"/>
        <xs:attribute name="request_type" type="stringType" use="required"/>
        <xs:attribute name="reply_type" type="stringType" use="required"/>
    </xs:complexType>

    <xs:complexType name="replierProfileType">
        <xs:all>
            <xs:element name="request_topic_name" type="stringType" minOccurs="0"/>
            <xs:element name="reply_topic_name" type="stringType" minOccurs="0"/>
            <xs:element name="publisher" type="publisherProfileType"/>
            <xs:element name="subscriber" type="subscriberProfileType"/>
        </xs:all>
        <xs:attribute name="profile_name" type="stringType" use="required"/>
        <xs:attribute name="service_name" type="stringType" use="required"/>
        <xs:attribute name="request_type" type="stringType" use="required"/>
        <xs:attribute name="reply_type" type="stringType" use="required"/>
    </xs:complexType>

    <xs:complexType name="TransportDescriptorListType">
      <xs:sequence>
        <xs:element name="transport_descriptor" type="rtpsTransportDescriptorType" maxOccurs="unbounded"/>
      </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="IntraprocessDeliveryType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="OFF"/>
            <xs:enumeration value="USER_DATA_ONLY"/>
            <xs:enumeration value="FULL"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="LibrarySettingsType">
        <xs:all minOccurs="0">
            <xs:element name="intraprocess_delivery" type="IntraprocessDeliveryType"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="rtpsTransportDescriptorType">
        <xs:all minOccurs="0">
            <xs:element name="transport_id" type="stringType"/>
            <xs:element name="type" type="stringType"/>
            <xs:element name="sendBufferSize" type="int32Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="receiveBufferSize" type="int32Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="TTL" type="uint8Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="non_blocking_send" type="boolType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="maxMessageSize" type="uint32Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="maxInitialPeersRange" type="uint32Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="interfaceWhiteList" type="addressListType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="wan_addr" type="stringType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="output_port" type="uint16Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="keep_alive_frequency_ms" type="uint32Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="keep_alive_timeout_ms" type="uint32Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="max_logical_port" type="uint16Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="logical_port_range" type="uint16Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="logical_port_increment" type="uint16Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="metadata_logical_port" type="uint16Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="listening_ports" type="portListType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="calculate_crc" type="boolType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="check_crc" type="boolType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="enable_tcp_nodelay" type="boolType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="tls" type="tlsConfigType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="tcp_negotiation_timeout" type="uint32" minOccurs="0" maxOccurs="1"/>
            <xs:element name="segment_size" type="uint32Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="port_queue_capacity" type="uint32Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="healthy_check_timeout_ms" type="uint32Type" minOccurs="0" maxOccurs="1"/>
            <xs:element name="rtps_dump_file" type="stringType" minOccurs="0" maxOccurs="1"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="stringListType">
        <xs:sequence>
            <xs:element name="id" type="stringType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="addressListType">
        <xs:sequence>
            <xs:element name="address" type="stringType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="portListType">
        <xs:sequence>
            <xs:element name="port" type="uint16Type" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="dds">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="profiles" type="profilesType" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="types" type="topicAttributesType" minOccurs="0" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:complexType name="profilesType">
        <xs:sequence>
            <xs:choice minOccurs="1" maxOccurs="unbounded">
                <xs:element name="library_settings" type="LibrarySettingsType" minOccurs="0" maxOccurs="1"/>
                <xs:element name="transport_descriptors" type="TransportDescriptorListType" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="participant" type="participantProfileType" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="publisher" type="publisherProfileType" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="subscriber" type="subscriberProfileType" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="topic" type="topicAttributesType" minOccurs="0" maxOccurs="unbounded"/>
                <xs:element name="types" type="topicAttributesType" minOccurs="0" maxOccurs="unbounded"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>

    <!-- DDS XML TYPES (FULL) -->
    <!-- Primitive types -->
    <xs:simpleType name="boolean">
        <xs:restriction base="xs:boolean"/>
    </xs:simpleType>

    <xs:simpleType name="char">
        <xs:restriction base="xs:string">
            <xs:length value="1" fixed="true"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="wchar">
        <xs:restriction base="xs:string"/>
    </xs:simpleType>

    <xs:simpleType name="double">
        <xs:restriction base="xs:double"/>
    </xs:simpleType>

    <xs:simpleType name="float">
        <xs:restriction base="xs:float"/>
    </xs:simpleType>

    <xs:simpleType name="octet">
        <xs:restriction base="xs:unsignedByte"/>
    </xs:simpleType>

    <xs:simpleType name="long">
        <xs:restriction base="xs:int"/>
    </xs:simpleType>

    <xs:simpleType name="long_long">
        <xs:restriction base="xs:long"/>
    </xs:simpleType>

    <xs:simpleType name="short">
        <xs:restriction base="xs:short"/>
    </xs:simpleType>

    <xs:simpleType name="string">
        <xs:restriction base="xs:string"/>
    </xs:simpleType>

    <xs:simpleType name="boundedString">
        <xs:restriction base="xs:string">
            <xs:maxLength value="10" fixed="true"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="unsigned_short">
        <xs:restriction base="xs:unsignedShort"/>
    </xs:simpleType>

    <xs:simpleType name="unsigned_long">
        <xs:restriction base="xs:unsignedInt"/>
    </xs:simpleType>

    <xs:simpleType name="unsigned_long_long">
        <xs:restriction base="xs:unsignedLong"/>
    </xs:simpleType>

    <!-- Used to declare structs, unions, etc. -->
    <xs:complexType name="complexTypeName">
        <xs:attribute name="type" type="string" use="required"/>
    </xs:complexType>

    <!-- Complex Types -->

    <xs:complexType name="caseDcl">
        <xs:sequence>
            <xs:choice minOccurs="1" maxOccurs="unbounded">
                <xs:element name="caseValue" type="string" minOccurs="1" maxOccurs="unbounded"/>
                <xs:element name="caseDiscriminator" type="short" minOccurs="1" maxOccurs="unbounded"/>
            </xs:choice>
            <xs:element name="member" type="memberDcl" minOccurs="1" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <!-- Aux declarations -->
    <xs:simpleType name="allTypes">
        <xs:restriction base="xs:string">
            <xs:enumeration value="boolean"/>
            <xs:enumeration value="char"/>
            <xs:enumeration value="wchar"/>
            <xs:enumeration value="octet"/>
            <xs:enumeration value="short"/>
            <xs:enumeration value="long"/>
            <xs:enumeration value="unsigned short"/>
            <xs:enumeration value="unsigned long"/>
            <xs:enumeration value="long long"/>
            <xs:enumeration value="unsigned long long"/>
            <xs:enumeration value="float"/>
            <xs:enumeration value="double"/>
            <xs:enumeration value="string"/>
            <xs:enumeration value="boundedString"/>
            <xs:enumeration value="complexTypeName"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="consumerDataType">
        <xs:all>
            <xs:element name="class" type="string"/>
            <xs:element name="property" type="propertyVectorType"/>
        </xs:all>
    </xs:complexType>

    <xs:complexType name="consumerVectorType">
        <xs:sequence>
            <xs:element name="consumer" type="consumerDataType"/>
        </xs:sequence>
    </xs:complexType>

    <!-- Log entry point TODO - Review Log parser and XSD
    <xs:element name="log">
        <xs:complexType>
            <xs:element name="use_default" type="boolType" minOccurs="0"/>
            <xs:sequence>
                <xs:element maxOccurs="consumer">
                    <xs:complexType>
                    <xs:element name="class" type="string" minOccurs="1" maxOccurs="1"/>
                    <xs:sequence>
                        <xs:element name="propertyType"/>
                    </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element> -->

    <!-- Dynamic Types -->
    <xs:element name="types">
        <xs:complexType>
            <xs:group ref="moduleElems"/>
        </xs:complexType>
    </xs:element>

    <xs:group name="moduleElems">
        <xs:sequence>
            <xs:choice maxOccurs="unbounded">
                <xs:element name="struct" type="structDcl" minOccurs="0"/>
                <xs:element name="union" type="unionDcl" minOccurs="0"/>
                <xs:element name="enum" type="enumDcl" minOccurs="0"/>
                <xs:element name="typedef" type="typedefDcl" minOccurs="0"/>
                <xs:element name="bitset" type="bitsetDcl" minOccurs="0"/>
                <xs:element name="bitmask" type="bitmaskDcl" minOccurs="0"/>
            </xs:choice>
        </xs:sequence>
    </xs:group>

    <xs:complexType name="bitsetDcl">
        <xs:sequence>
            <xs:choice maxOccurs="unbounded">
                <xs:element name="bitfield" type="bitfieldDcl" minOccurs="1"/>
            </xs:choice>
        </xs:sequence>
        <xs:attribute name="name" type="stringType" use="required"/>
        <xs:attribute name="baseType" type="stringType" use="optional"/>
    </xs:complexType>

    <xs:complexType name="bitmaskDcl">
        <xs:sequence>
            <xs:element name="bit_value" type="bit_valueType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="name" use="required"/>
        <xs:attribute name="bit_bound" use="optional"/>
    </xs:complexType>

    <xs:complexType name="structDcl">
        <xs:sequence>
            <xs:choice maxOccurs="unbounded">
                <xs:element name="member" type="memberDcl" minOccurs="1"/>
            </xs:choice>
        </xs:sequence>
        <xs:attribute name="name" type="stringType" use="required"/>
        <xs:attribute name="baseType" type="stringType" use="optional"/>
    </xs:complexType>

    <xs:complexType name="unionDcl">
        <xs:sequence>
            <xs:element name="discriminator" type="string" minOccurs="1"/>
            <xs:sequence maxOccurs="unbounded">
                <xs:element name="case" type="caseDcl" minOccurs="1"/>
            </xs:sequence>
        </xs:sequence>
        <xs:attribute name="name" type="stringType" use="required"/>
    </xs:complexType>

    <xs:complexType name="typedefDcl">
        <xs:attribute name="name" type="stringType" use="required"/>
        <xs:attribute name="type" type="string" use="required"/>
        <xs:attribute name="key_type" type="string" use="optional"/>
        <xs:attribute name="arrayDimensions" type="string" use="optional"/>
        <xs:attribute name="nonBasicTypeName" type="string" use="optional"/>
        <xs:attribute name="sequenceMaxLength" type="string" use="optional"/>
        <xs:attribute name="mapMaxLength" type="string" use="optional"/>
    </xs:complexType>

    <xs:complexType name="enumeratorType">
        <xs:attribute name="name" type="stringType" use="required"/>
        <xs:attribute name="value" type="stringType" use="optional"/>
    </xs:complexType>

    <xs:complexType name="bit_valueType">
        <xs:attribute name="name" type="stringType" use="required"/>
        <xs:attribute name="position" type="int16Type" use="optional"/>
    </xs:complexType>

    <xs:complexType name="enumDcl">
        <xs:sequence>
            <xs:element name="enumerator" type="enumeratorType" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="name" type="stringType" use="required"/>
        <xs:attribute name="bitBound" type="int16Type" use="optional"/>
    </xs:complexType>

    <xs:complexType name="memberDcl">
        <xs:sequence>
            <xs:element name="member" type="memberDcl" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="name" type="string" use="required"/>
        <xs:attribute name="type" type="string" use="required"/>
        <xs:attribute name="arrayDimensions" type="string" use="optional"/>
        <xs:attribute name="sequenceMaxLength" type="string" use="optional"/>
        <xs:attribute name="mapMaxLength" type="string" use="optional"/>
        <xs:attribute name="nonBasic" type="string" use="optional"/>
        <xs:attribute name="nonBasicTypeName" type="string" use="optional"/>
    </xs:complexType>

    <xs:complexType name="bitfieldDcl">
        <xs:attribute name="name" type="stringType" use="optional"/>
        <xs:attribute name="type" type="stringType" use="optional"/>
        <xs:attribute name="bit_bound" type="int16Type" use="required"/>
    </xs:complexType>

</xs:schema>
