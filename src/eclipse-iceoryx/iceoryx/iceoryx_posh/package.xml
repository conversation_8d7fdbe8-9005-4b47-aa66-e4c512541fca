<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
    <name>iceoryx_posh</name>
    <version>2.0.6</version>
    <description>Eclipse iceoryx inter-process-communication (IPC) middleware Posix Shared Memory Library and middleware daemon (RouDi)</description>
    <maintainer email="<EMAIL>">Eclipse Foundation, Inc.</maintainer>
    <license>Apache 2.0</license>
    <url type="website">https://iceoryx.io</url>
    <url type="bugtracker">https://bgithub.xyz/eclipse-iceoryx/iceoryx/issues</url>
    <url type="repository">https://bgithub.xyz/eclipse-iceoryx/iceoryx</url>

    <buildtool_depend>cmake</buildtool_depend>
    <buildtool_depend>git</buildtool_depend>

    <depend>iceoryx_hoofs</depend>

    <doc_depend>doxygen</doc_depend>

    <export>
        <build_type>cmake</build_type>
    </export>
</package>
