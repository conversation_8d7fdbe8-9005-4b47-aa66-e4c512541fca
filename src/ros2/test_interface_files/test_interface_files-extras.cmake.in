# Copyright 2019 Open Source Robotics Foundation, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# generated from test_interface_files/test_interface_files-extras.cmake.in

set(@PROJECT_NAME@_INTERFACE_FILES_BASEPATH "${@PROJECT_NAME@_DIR}")
get_filename_component(@PROJECT_NAME@_INTERFACE_FILES_BASEPATH "${@PROJECT_NAME@_INTERFACE_FILES_BASEPATH}" DIRECTORY)

set(@PROJECT_NAME@_MSG_FILES "")
foreach(msg @msg_files@)
  list(APPEND @PROJECT_NAME@_MSG_FILES
    "${@PROJECT_NAME@_INTERFACE_FILES_BASEPATH}:${msg}")
endforeach()
set(@PROJECT_NAME@_SRV_FILES "")
foreach(srv @srv_files@)
  list(APPEND @PROJECT_NAME@_SRV_FILES
    "${@PROJECT_NAME@_INTERFACE_FILES_BASEPATH}:${srv}")
endforeach()
set(@PROJECT_NAME@_ACTION_FILES "")
foreach(action @action_files@)
  list(APPEND @PROJECT_NAME@_ACTION_FILES
    "${@PROJECT_NAME@_INTERFACE_FILES_BASEPATH}:${action}")
endforeach()
set(@PROJECT_NAME@_IDL_FILES "")
foreach(idl @idl_files@)
  list(APPEND @PROJECT_NAME@_IDL_FILES
    "${@PROJECT_NAME@_INTERFACE_FILES_BASEPATH}:${idl}")
endforeach()
