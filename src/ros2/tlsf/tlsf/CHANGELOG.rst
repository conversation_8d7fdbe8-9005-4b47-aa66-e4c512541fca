^^^^^^^^^^^^^^^^^^^^^^^^^^
Changelog for package tlsf
^^^^^^^^^^^^^^^^^^^^^^^^^^

0.7.0 (2022-03-01)
------------------
* Install headers to include/${PROJECT_NAME} (`#11 <https://github.com/ros2/tlsf/issues/11>`_)
* Contributors: <PERSON>

0.6.0 (2021-12-13)
------------------
* Export a modern CMake target instead of old-style variables (`#10 <https://github.com/ros2/tlsf/issues/10>`_)
* Contributors: <PERSON>

0.5.2 (2021-03-18)
------------------
* Switch to standard __VA_ARGS_\_. (`#9 <https://github.com/ros2/tlsf/issues/9>`_)
* Contributors: <PERSON>

0.5.1 (2020-12-08)
------------------
* Enable basic warnings (`#8 <https://github.com/ros2/tlsf/issues/8>`_)
* Contributors: Audrow <PERSON>

0.5.0 (2018-06-26)
------------------
* Change maintainer to me.
  Signed-off-by: Chris Lalancette <<EMAIL>>
* Contributors: Chris Lalancette

0.4.0 (2017-12-08)
------------------
* 0.0.3
* 0.0.2
* update schema url
* add schema to manifest files
* Merge pull request `#4 <https://github.com/ros2/tlsf/issues/4>`_ from ros2/cmake35
  require CMake 3.5
* require CMake 3.5
* Disable on Android (`#3 <https://github.com/ros2/tlsf/issues/3>`_)
* Merge pull request `#2 <https://github.com/ros2/tlsf/issues/2>`_ from ros2/ctest_build_testing
  use CTest BUILD_TESTING
* use CTest BUILD_TESTING
* Merge pull request `#1 <https://github.com/ros2/tlsf/issues/1>`_ from ros2/dirk-thomas-patch-1
  remove -fvisibility-inlines-hidden
* remove -fvisibility-inlines-hidden
  > warning: command line option ‘-fvisibility-inlines-hidden’ is valid for C++/ObjC++ but not for C
* Skip Windows and OSX
* Import tlsf allocator
* Contributors: Dirk Thomas, Esteve Fernandez, Jackie Kay
