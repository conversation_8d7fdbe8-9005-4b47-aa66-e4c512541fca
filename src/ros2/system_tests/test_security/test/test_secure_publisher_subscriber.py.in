# generated from test_communication/test/test_secure_publisher_subscriber.py.in

import os
# import sys
# import time

from launch import LaunchDescription
from launch.actions import ExecuteProcess

import launch_testing
import launch_testing.actions

import unittest


def generate_test_description():
    # TODO Timestamping tests via the node's namespace is no longer appropriate,
    # given the FQN is used to lookup security artifacts from secure root directory.
    # namespace = '/test_time_%s' % time.strftime('%H_%M_%S', time.gmtime())
    namespace = '/'

    launch_description = LaunchDescription()

    publisher_cmd = [
        '@TEST_PUBLISHER_EXECUTABLE@', '@TEST_MESSAGE_TYPE@', namespace
    ]
    subscriber_cmd = [
        '@TEST_SUBSCRIBER_EXECUTABLE@', '@TEST_MESSAGE_TYPE@',
        '@SUBSCRIBER_SHOULD_TIMEOUT@', namespace
    ]

    publisher_env = dict(os.environ)
    subscriber_env = dict(os.environ)

    # TODO (mikaelarguedas) Do we want to test any of this across client libraries??
    # if '@TEST_PUBLISHER_RCL@' == 'rclpy':
    #     publisher_cmd.insert(0, sys.executable)
    #     publisher_env['PYTHONUNBUFFERED'] = '1'
    # if '@TEST_SUBSCRIBER_RCL@' == 'rclpy':
    #     subscriber_cmd.insert(0, sys.executable)
    #     subscriber_env['PYTHONUNBUFFERED'] = '1'

    publisher_env['RCL_ASSERT_RMW_ID_MATCHES'] = '@PUBLISHER_RMW@'
    publisher_env['RMW_IMPLEMENTATION'] = '@PUBLISHER_RMW@'
    publisher_env['ROS_SECURITY_ENABLE'] = '@PUBLISHER_ROS_SECURITY_ENABLE@'
    publisher_env['ROS_SECURITY_STRATEGY'] = '@PUBLISHER_ROS_SECURITY_STRATEGY@'
    publisher_env['ROS_SECURITY_KEYSTORE'] = '@PUBLISHER_ROS_SECURITY_KEYSTORE@'

    launch_description.add_action(ExecuteProcess(
        cmd=publisher_cmd,
        name='test_publisher',
        env=publisher_env,
    ))

    subscriber_env['RCL_ASSERT_RMW_ID_MATCHES'] = '@SUBSCRIBER_RMW@'
    subscriber_env['RMW_IMPLEMENTATION'] = '@SUBSCRIBER_RMW@'
    subscriber_env['ROS_SECURITY_ENABLE'] = '@SUBSCRIBER_ROS_SECURITY_ENABLE@'
    subscriber_env['ROS_SECURITY_STRATEGY'] = '@SUBSCRIBER_ROS_SECURITY_STRATEGY@'
    subscriber_env['ROS_SECURITY_KEYSTORE'] = '@SUBSCRIBER_ROS_SECURITY_KEYSTORE@'

    subscriber_process = ExecuteProcess(
        cmd=subscriber_cmd,
        name='test_subscriber',
        env=subscriber_env,
    )
    launch_description.add_action(subscriber_process)

    launch_description.add_action(
        launch_testing.actions.ReadyToTest()
    )
    return launch_description, locals()


class TestSecurePublisherSubscriber(unittest.TestCase):

    def test_subscriber_terminates_in_a_finite_amount_of_time(self, proc_info, subscriber_process):
        """Test that the subscriber terminates after a finite amount of time."""
        proc_info.assertWaitForShutdown(process=subscriber_process, timeout=30)


@launch_testing.post_shutdown_test()
class TestSecurePublisherSubscriberAfterShutdown(unittest.TestCase):

    def test_processes_finished_gracefully(self, proc_info):
        """Test that both executables finished gracefully."""
        launch_testing.asserts.assertExitCodes(
            proc_info,
            [launch_testing.asserts.EXIT_OK],
            'test_subscriber'
        )
        launch_testing.asserts.assertExitCodes(
            proc_info,
            process='test_publisher'
        )
