<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format2.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="2">
  <name>test_rclcpp</name>
  <version>0.12.4</version>
  <description>
    Test rclcpp API.
    Each test is run with every available rmw implementation.
  </description>
  <maintainer email="<EMAIL>">Adity<PERSON> Pande</maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache License 2.0</license>

  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>
  <author email="<EMAIL>"><PERSON></author>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <test_depend>ament_cmake_gtest</test_depend>
  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>launch</test_depend>
  <test_depend>launch_testing</test_depend>
  <test_depend>launch_testing_ament_cmake</test_depend>
  <test_depend>launch_testing_ros</test_depend>
  <test_depend>rclcpp</test_depend>
  <test_depend>rcpputils</test_depend>
  <test_depend>rmw_implementation</test_depend>
  <test_depend>rmw_implementation_cmake</test_depend>
  <test_depend>rosidl_default_generators</test_depend>
  <test_depend>rosidl_default_runtime</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
