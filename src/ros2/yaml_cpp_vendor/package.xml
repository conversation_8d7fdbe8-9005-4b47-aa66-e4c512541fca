<?xml version="1.0"?>
<?xml-model
  href="http://download.ros.org/schema/package_format2.xsd"
  schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="2">
  <name>yaml_cpp_vendor</name>
  <version>8.0.2</version>
  <description>
    Wrapper around yaml-cpp, it provides a fixed CMake module and an ExternalProject build of it.
  </description>
  <maintainer email="<EMAIL>">Audrow Nash</maintainer>
  <license>Apache License 2.0</license>  <!-- the contents of this package are Apache 2.0 -->
  <license>MIT</license>  <!-- yaml-cpp is MIT -->

  <author email="<EMAIL>"><PERSON></author>

  <url type="website">https://bgithub.xyz/jbeder/yaml-cpp</url>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>yaml-cpp</depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
