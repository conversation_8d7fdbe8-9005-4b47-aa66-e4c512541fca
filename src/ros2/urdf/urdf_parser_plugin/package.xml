<package format="2">
  <name>urdf_parser_plugin</name>
  <version>2.6.1</version>
  <description>
    This package contains a C++ base class for URDF parsers.
  </description>

  <author email="<EMAIL>"><PERSON><PERSON></author>
  <author email="jacqueline<PERSON><EMAIL>"><PERSON></author>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>BSD</license>

  <!--url type="website">http://ros.org/wiki/urdf</url-->
  <url type="repository">https://bgithub.xyz/ros2/urdf</url>
  <url type="bugtracker">https://bgithub.xyz/ros2/urdf/issues</url>

  <buildtool_depend>ament_cmake_ros</buildtool_depend>
  <!-- Use ROS 2 urdfdom_headers package because upstream is out of date -->
  <build_depend>urdfdom_headers</build_depend>
  <build_export_depend>urdfdom_headers</build_export_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>

</package>
