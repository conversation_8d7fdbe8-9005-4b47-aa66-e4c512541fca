<?xml version="1.0" ?>

<robot name="pr2">
    <joint name="fl_caster_rotation_joint" type="revolute">
      <axis xyz="0 0 1"/>
      <limit effort="100" k_velocity="10" velocity="100"/>
      <calibration reference_position="0.0" values="1.5 -1"/>
      <joint_properties damping="0.0" friction="0.0"/>
      <origin rpy="0 0 1" xyz="0.2225 0.2225 0.0282"/>
      <parent link="base_link"/>
      <child link="fl_caster_rotation_link"/>
    </joint>
    <link name="fl_caster_rotation_link">
      <inertial>
        <mass value="3.473082"/>
        <origin xyz="0 0 0.07"/>
        <inertia ixx="0.012411765597" ixy="-0.000711733678" ixz="0.00050272983" iyy="0.015218160428" iyz="-0.000004273467" izz="0.011763977943"/>
      </inertial>
    </link>

    <joint name="fr_caster_rotation_joint" type="revolute">
      <axis xyz="0 0 1"/>
      <limit effort="100" k_velocity="10" velocity="100"/>
      <calibration reference_position="0.0" values="1.5 -1"/>
      <joint_properties damping="0.0" friction="0.0"/>
      <origin rpy="0 0 1" xyz="0.2225 0.2225 0.0282"/>
      <parent link="world"/>
      <child link="fr_caster_rotation_link"/>
    </joint>
    <link name="fr_caster_rotation_link">

      <inertial>
        <mass value="3.473082"/>
        <origin xyz="0 0 0.07"/>
        <inertia ixx="0.012411765597" ixy="-0.000711733678" ixz="0.00050272983" iyy="0.015218160428" iyz="-0.000004273467" izz="0.011763977943"/>
      </inertial>
    </link>

</robot>
