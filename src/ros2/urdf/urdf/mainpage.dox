/**
\mainpage
\htmlinclude manifest.html

urdf::Model is a class containing robot model data structure.
Every Robot Description File (URDF) can be described as a list of Links (urdf::Model::links_) and Joints (urdf::Model::joints_).
The connection between links(nodes) and joints(edges) should define a tree (i.e. 1 parent link, 0+ children links).
\li Here is an example Robot Description Describing a Parent Link 'P', a Child Link 'C', and a Joint 'J'
 @verbatim
  <joint name="J" type="revolute">
    <dynamics damping="1" friction="0"/>
    <limit lower="0.9" upper="2.1" effort="1000" velocity="1"/>
    <safety_controller soft_lower_limit="0.7" soft_upper_limit="2.1" k_position="1" k_velocity="1" />
    <calibration reference_position="0.7" />
    <mimic joint="J100" offset="0" multiplier="0.7" />

    <!-- origin: origin of the joint in the parent frame -->
    <!-- child link frame is the joint frame -->
    <!-- axis is in the joint frame -->
    <origin xyz="0 0 0" rpy="0 0 0"/> 
    <axis xyz="0 1 0"/> 
    <parent link="P"/>
    <child link="C"/>
  </joint>

  <link name="C">
    <inertial>
      <mass value="10"/>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>

    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="1 1 1"/>
      </geometry>
      <material name="Green"/>
    </visual>

    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="1.01 1.01 1.01"/>
      </geometry>
      <contact_coefficient mu="0"  resitution="0"  k_p="0"  k_d="0" />
    </collision>
  </link>

  <material name="Green">
    <texture filename="...texture file..." />
    <!--color rgb="255 255 255" /-->
  </material>
 @endverbatim



\section codeapi Code API

The URDF parser API contains the following methods:
  \li Parse and build tree from XML: urdf::Model::initXml
  \li Parse and build tree from File: urdf::Model::initFile
  \li Parse and build tree from String: urdf::Model::initString
  \li Get Root Link: urdf::Model::getRoot
  \li Get Link by name urdf::Model::getLink
  \li Get all Link's urdf::Model::getLinks
  \li Get Joint by name urdf::Model::getJoint

<!--
\section rosapi ROS API

Names are very important in ROS because they can be remapped on the
command-line, so it is VERY IMPORTANT THAT YOU LIST NAMES AS THEY
APPEAR IN THE CODE. You should list names of every topic, service and
parameter used in your code. There is a template below that you can
use to document each node separately.

List of nodes:
- \b node_name1
- \b node_name2
-->

<!-- START: copy from here to 'END' for each node 

<hr>

\subsection node_name node_name

node_name does (provide a basic description of your node)

\subsubsection Usage
\verbatim
$ node_type1 [standard ROS args]
\endverbatim

\par Example

\verbatim
$ node_type1
\endverbatim


\subsubsection topics ROS topics

Subscribes to:
- \b "in": [std_msgs/FooType] description of in

Publishes to:
- \b "out": [std_msgs/FooType] description of out


\subsubsection parameters ROS parameters

Reads the following parameters from the parameter server

- \b "~param_name" : \b [type] description of param_name
- \b "~my_param" : \b [string] description of my_param

Sets the following parameters on the parameter server

- \b "~param_name" : \b [type] description of param_name


\subsubsection services ROS services
- \b "foo_service": [std_srvs/FooType] description of foo_service


END: copy for each node -->


<!-- START: Uncomment if you have any command-line tools

\section commandline Command-line tools

This section is a catch-all for any additional tools that your package
provides or uses that may be of use to the reader. For example:

- tools/scripts (e.g. rospack, roscd)
- roslaunch .launch files
- xmlparam files

\subsection script_name script_name

Description of what this script/file does.

\subsubsection Usage
\verbatim
$ ./script_name [args]
\endverbatim

\par Example

\verbatim
$ ./script_name foo bar
\endverbatim

END: Command-Line Tools Section -->

*/
