cmake_minimum_required(VERSION 3.5)
project(tinyxml)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)
add_definitions(-DTIXML_USE_STL)
add_library(tinyxml tinyxml.cpp tinyxmlerror.cpp tinyxmlparser.cpp)
set_target_properties(tinyxml PROPERTIES
                      INSTALL_NAME_DIR ${CMAKE_INSTALL_PREFIX}/lib)

install(TARGETS tinyxml
        LIBRARY DESTINATION lib
        ARCHIVE DESTINATION lib
        RUNTIME DESTINATION lib)
install(FILES tinyxml.h tinystr.h
        DESTINATION include)
